import { Checkbox } from '@chakra-ui/checkbox';
import {
  Box,
  type BoxProps,
  Icon,
  IconButton,
  Spinner,
} from '@chakra-ui/react';
import { Table, Tbody, Td, Th, Thead, Tr } from '@chakra-ui/table';
import { memo, useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FiTrash2 } from 'react-icons/fi';
import type { MerchantQuery } from 'shared/api';
import { Confirmation } from 'shared/components';
import { useShowMessage } from 'shared/hooks/alerts';
import { useIsMobileLayout } from 'shared/hooks/layout';
import {
  useDetachMerchantUser,
  useMerchantUsers,
  useUpdateMerchantUserPermissions,
} from 'shared/hooks/merchant';
import { useCurrentUser } from 'shared/hooks/user';
import { MerchantPermissions } from 'shared/types';
import { hasPermissions } from 'shared/utils';

const spacing: BoxProps = {
  py: 0,
  pl: 0,
  pr: 5,
};

const borderStyles: BoxProps = {
  borderBottom: '1px solid',
  borderColor: 'gray.200',
};

const headerStyles: BoxProps = {
  ...spacing,
  ...borderStyles,
  h: 9,
  textStyle: 'body2-highlight',
  textTransform: 'none',

  color: 'neutral.800',
};

const cellStyles: BoxProps = {
  ...spacing,
  ...borderStyles,
  h: '2.75rem',
  textStyle: 'body2',
};

const firstColumnStyles: BoxProps = {
  pl: [5, null, '0.625rem'],
  pr: [5, null, '0.625rem'],
  whiteSpace: 'nowrap',
};

export const EmployeesTable = () => {
  const { t } = useTranslation('settings');
  const isMobileLayout = useIsMobileLayout();

  const { users } = useMerchantUsers();
  const employees = users || [];
  const tableRef = useRef<HTMLDivElement>(null);
  const [isOverflown, setIsOverflown] = useState(false);

  const showMessage = useShowMessage();

  const { updatePermissions } = useUpdateMerchantUserPermissions();
  const { detachMerchantUser } = useDetachMerchantUser();

  const onUpdatePermissions = useCallback(
    async (userId: number, permissionBits: number) => {
      await updatePermissions({ userId, permissionBits });
      showMessage(t('notifications.employee-permissions-updated'));
    },
    [t, showMessage, updatePermissions],
  );

  const onRemoveEmployee = useCallback(
    async (userId: number) => {
      await detachMerchantUser({ userId });
      showMessage(t('notifications.employee-removed'));
    },
    [t, showMessage, detachMerchantUser],
  );

  return (
    <Box
      display="flex"
      onScroll={(e) => {
        const scrollLeft = (e.target as HTMLDivElement).scrollLeft;
        if (scrollLeft > 0 && !isOverflown) {
          setIsOverflown(true);
        } else if (scrollLeft === 0 && isOverflown) {
          setIsOverflown(false);
        }
      }}
      overflowX="auto"
      overflowY="hidden"
      ref={tableRef}
      width="100%"
    >
      {!!isMobileLayout && (
        <Table
          bgColor="white"
          boxShadow={isOverflown ? '0px 0px 12px rgba(0, 0, 0, 0.25)' : 'none'}
          display="table"
          flexShrink={0}
          left={0}
          position="sticky"
          width="auto"
          zIndex={1}
        >
          <Thead>
            <Tr>
              <Th {...(headerStyles as any)} {...(firstColumnStyles as any)}>
                {t('employees.table.name')}
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {employees.map((employee) => (
              <Tr key={employee.id}>
                <Td {...(cellStyles as any)} {...(firstColumnStyles as any)}>
                  {[employee.profile?.first_name, employee.profile?.last_name]
                    .filter((part) => !!part)
                    .join(' ')}
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      )}
      <Table flexGrow={1}>
        <Thead>
          <Tr>
            <Th
              {...(headerStyles as any)}
              {...(firstColumnStyles as any)}
              display={['none', null, 'table-cell']}
            >
              {t('employees.table.name')}
            </Th>
            <Th {...(headerStyles as any)}>{t('employees.table.email')}</Th>
            <Th {...(headerStyles as any)}>
              {t('employees.roles.merchant-admin')}
            </Th>
            <Th {...(headerStyles as any)}>{t('employees.roles.cashier')}</Th>
            <Th {...(headerStyles as any)}>
              {t('employees.roles.accountant')}
            </Th>
            <Th {...(headerStyles as any)}>{t('employees.roles.developer')}</Th>
            <Th {...(headerStyles as any)} p={0} w="2.75rem" />
          </Tr>
        </Thead>
        <Tbody>
          {employees.map((employee) => (
            <TableRow
              employee={employee}
              key={employee.id}
              onPermissionChange={onUpdatePermissions}
              onRemoveEmployee={onRemoveEmployee}
            />
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};
// merchantQuery_merchant_users
type TableRowProps = {
  employee: NonNullOrUndefined<
    ArrayElement<
      NonNullOrUndefined<NonNullOrUndefined<MerchantQuery['merchant']>['users']>
    >
  >;
  onPermissionChange: (
    userId: number,
    newPermissionBits: number,
  ) => Promise<void>;
  onRemoveEmployee: (userId: number) => Promise<void>;
};

const roles = [
  MerchantPermissions.Admin,
  MerchantPermissions.Cashier,
  MerchantPermissions.Accountant,
  MerchantPermissions.Developer,
];

const TableRow = memo(
  ({
    employee,
    onPermissionChange,
    onRemoveEmployee,
  }: TableRowProps): JSX.Element => {
    const { t } = useTranslation('settings');
    const { user } = useCurrentUser();
    const isCurrentUser = user?.id === employee.id;
    const [permissionChangeInProgress, setPermissionChangeInProgress] =
      useState<MerchantPermissions>();
    const [isDeleting, setIsDeleting] = useState<boolean>(false);

    return (
      <Tr data-cy={`employees-table-row-${employee?.id}`}>
        <Td
          data-cy="name-cell"
          {...cellStyles}
          {...firstColumnStyles}
          display={['none', null, 'table-cell']}
        >
          {[employee.profile?.first_name, employee.profile?.last_name]
            .filter((part) => !!part)
            .join(' ')}
        </Td>
        <Td data-cy="email-cell" {...cellStyles}>
          {employee.email ?? ''}
        </Td>
        {roles.map((permission) => {
          const isDisabled =
            (isCurrentUser && permission === MerchantPermissions.Admin) ||
            !(permissionChangeInProgress == null) ||
            isDeleting;
          return (
            <Td
              data-cy={`role-cell-${permission}`}
              key={permission}
              {...cellStyles}
            >
              {permissionChangeInProgress === permission ? (
                <Spinner boxSize={5} color="primary.800" />
              ) : (
                <Checkbox
                  isChecked={hasPermissions(employee.merchant_permission_bits, [
                    permission,
                  ])}
                  disabled={isDisabled}
                  onChange={async () => {
                    setPermissionChangeInProgress(permission);
                    await onPermissionChange(
                      employee.id,
                      employee.merchant_permission_bits ^ permission,
                    );
                    setPermissionChangeInProgress(undefined);
                  }}
                />
              )}
            </Td>
          );
        })}
        <Td data-cy="remove-cell" {...cellStyles} pr={[3, null, 0]}>
          {!isCurrentUser && (
            <Confirmation
              actionText={t('employees.remove-confirmation.submit')}
              cyPrefix="remove-employee"
              onAction={async () => {
                setIsDeleting(true);
                await onRemoveEmployee(employee.id);
              }}
              popoverPlacement="bottom-end"
              title={t('employees.remove-confirmation.title')}
              trigger={
                <IconButton
                  _active={{ bg: 'neutral.150' }}
                  _focus={{ bg: 'neutral.150' }}
                  _hover={{ bg: 'neutral.100' }}
                  aria-label="back"
                  borderRadius={0}
                  boxSize="2.75rem"
                  data-cy="remove-employee-btn"
                  disabled={!(permissionChangeInProgress == null) || isDeleting}
                  mb="-1px"
                  minW="2.75rem"
                  variant="ghost"
                >
                  <Icon as={FiTrash2} boxSize={5} color="primary.800" />
                </IconButton>
              }
            />
          )}
        </Td>
      </Tr>
    );
  },
);
