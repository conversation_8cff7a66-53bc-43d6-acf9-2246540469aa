import { useReactiveVar } from '@apollo/client';
import { Box, Text, VStack } from '@chakra-ui/react';
import { Table, Tbody, Th, Thead, Tr } from '@chakra-ui/table';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader, NoData } from 'shared/components';
import {
  LocizeFinanceKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { useGetPages, useSetDefaultTableState } from 'shared/hooks/utils';

import { InvoicesCollectionByPage } from './components';
import { useInvoicesList } from './InvoicesTable.hooks';
import {
  defaultInvoicesTableState,
  invoicesTableState,
} from './InvoicesTable.utils';

const TABLE_COLUMNS: Array<{
  name: string;
  textAlign?: TypographyProps['textAlign'];
}> = [
  {
    name: LocizeFinanceKeys.INVOICES_TABLE_COLUMN_NO,
  },
  {
    name: LocizeFinanceKeys.INVOICES_TABLE_COLUMN_DUE_DATE,
  },
  {
    name: LocizeFinanceKeys.INVOICES_TABLE_COLUMN_DUE_AMOUNT,
  },
  {
    name: '',
  },
];

export const InvoicesTable = () => {
  const { data, loading } = useInvoicesList({ page: 1 });
  const { t } = useTranslation(LocizeNamespaces.FINANCE);
  const tableState = useReactiveVar(invoicesTableState);
  const { pages } = useGetPages(tableState.page);
  const [portalEl, setPortalEl] = useState<HTMLDivElement | null>(null);

  useSetDefaultTableState(invoicesTableState, defaultInvoicesTableState);

  return (
    <VStack align="stretch" height="100%" overflow="auto" gap={6}>
      <Box>
        <Text fontSize="1.125rem" fontWeight={600}>
          {t(LocizeFinanceKeys.INVOICES_TABLE_TITLE)}
        </Text>
      </Box>
      {loading ? (
        <Loader height="65vh" />
      ) : data?.invoices?.data?.length ? (
        <Box mx={-5} overflow="auto">
          <Box minW="100%" px={[5, null, 10]} width="fit-content">
            <Table data-cy="transactions-table" position="relative">
              <Thead
                bg="white"
                borderBottom="1px solid"
                borderColor="neutral.100"
                left={0}
                position="sticky"
                top={0}
              >
                <Tr>
                  {TABLE_COLUMNS.map(({ name, textAlign }, index) => (
                    <Th
                      borderBottom="1px solid"
                      borderColor="neutral.100"
                      color="neutral.800"
                      key={name || index.toString()}
                      pl={[0, null, 3]}
                      pr={5}
                      py={2}
                      textAlign={textAlign}
                      textStyle="body2-highlight"
                      textTransform="none"
                      whiteSpace="nowrap"
                    >
                      {!!name && t(name)}
                    </Th>
                  ))}
                </Tr>
              </Thead>
              <Tbody>
                {pages.map((page) => (
                  <InvoicesCollectionByPage
                    key={page}
                    page={page}
                    portalElement={portalEl}
                  />
                ))}
              </Tbody>
            </Table>
          </Box>
          <Box
            px={[0, null, 5]}
            ref={(ref) => {
              setPortalEl(ref);
            }}
          />
        </Box>
      ) : (
        <NoData
          height="100%"
          text={t(LocizeFinanceKeys.INVOICES_TABLE_EMPTY_DATA_TEXT)}
        />
      )}
    </VStack>
  );
};
